// 简单测试定时刷新 profiles 功能
class MockMessenger {
  async request(messageType, data) {
    console.log(`Mock messenger request: ${messageType}`, data);
    if (messageType === "getControlPlaneSessionInfo") {
      return { sessionId: "mock-session", userId: "mock-user" };
    }
    return null;
  }
}

class MockConfigHandler {
  async updateControlPlaneSessionInfo(sessionInfo) {
    console.log("Mock updateControlPlaneSessionInfo called with:", sessionInfo);
    return Promise.resolve();
  }

  async refreshAll() {
    console.log("Mock refreshAll called");
    return Promise.resolve();
  }
}

class TestCore {
  constructor() {
    this.messenger = new MockMessenger();
    this.configHandler = new MockConfigHandler();
    this.configReloadTimer = null;
    this.startConfigReloadTimer();
  }

  /**
   * 执行 refreshProfiles 的逻辑
   */
  async refreshProfilesHandler() {
    const sessionInfo = await this.messenger.request(
      "getControlPlaneSessionInfo",
      {
        silent: true,
        useOnboarding: false,
      },
    );
    if (sessionInfo) {
      await this.configHandler.updateControlPlaneSessionInfo(sessionInfo);
    }

    await this.configHandler.refreshAll();
  }

  /**
   * 启动配置重载定时器，每24小时执行一次
   */
  startConfigReloadTimer() {
    // 为了测试，使用3秒而不是24小时
    const TEST_INTERVAL = 3 * 1000; // 3秒
    
    this.configReloadTimer = setInterval(async () => {
      try {
        console.log("定时刷新配置开始...");
        await this.refreshProfilesHandler();
        console.log("定时刷新配置完成");
      } catch (error) {
        console.error("定时刷新配置失败:", error);
      }
    }, TEST_INTERVAL);
    
    console.log("配置定时刷新已启动，将每3秒执行一次（测试模式）");
  }

  /**
   * 停止配置重载定时器
   */
  stopConfigReloadTimer() {
    if (this.configReloadTimer) {
      clearInterval(this.configReloadTimer);
      this.configReloadTimer = null;
      console.log("配置定时刷新已停止");
    }
  }

  /**
   * 清理资源
   */
  dispose() {
    this.stopConfigReloadTimer();
  }
}

// 运行测试
console.log("开始测试定时刷新 profiles 功能...");
const testCore = new TestCore();

// 10秒后停止测试
setTimeout(() => {
  console.log("测试结束，停止定时器");
  testCore.dispose();
  process.exit(0);
}, 10000);
